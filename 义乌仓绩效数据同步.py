#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
义乌仓绩效数据拉取脚本
读取本地Excel中的义乌仓绩效数据，并将其写入钉钉在线表格。
"""

import pandas as pd
from datetime import datetime, timedelta
import json
import os
import urllib3
import argparse
import time
from dingtalk_sheet_utils import DingTalkSheetUtils

# 禁用InsecureRequestWarning
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def load_config(config_path='config.json'):
    """加载配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 配置文件 {config_path} 未找到，请检查路径是否正确。")
        return None
    except json.JSONDecodeError:
        print(f"❌ 配置文件 {config_path} 格式错误，请检查JSON语法。")
        return None

def get_performance_data(file_path, sheet_name, max_retries=3):
    """
    从Excel文件中读取指定工作表的绩效数据，支持重试。

    Args:
        file_path (str): Excel文件路径。
        sheet_name (str): 工作表名称。
        max_retries (int): 最大重试次数。

    Returns:
        pd.DataFrame or None: 如果成功则返回数据帧，否则返回None。
    """
    for attempt in range(max_retries):
        try:
            print(f"📖 尝试读取Excel文件 (第{attempt + 1}次/共{max_retries}次)...")
            # 读取Excel文件，并将所有列都作为字符串读取以避免格式问题
            df = pd.read_excel(file_path, sheet_name=sheet_name, dtype=str)
            # 将'日期'列转换为datetime对象，以便进行日期比较
            df['日期'] = pd.to_datetime(df['日期'], errors='coerce')
            print(f"✅ 成功读取Excel文件，共{len(df)}行数据")
            return df
        except FileNotFoundError:
            print(f"❌ Excel文件未找到: {file_path}")
            return None
        except ValueError as e:
            if 'Worksheet named' in str(e) and sheet_name in str(e):
                print(f"❌ 在Excel文件中找不到名为 '{sheet_name}' 的工作表。")
                return None
            else:
                print(f"❌ 读取Excel时发生未知错误: {e}")
                if attempt < max_retries - 1:
                    print(f"⏳ 等待2秒后重试...")
                    time.sleep(2)
                else:
                    return None
        except Exception as e:
            print(f"❌ 读取Excel时发生异常: {e}")
            if attempt < max_retries - 1:
                print(f"⏳ 等待2秒后重试...")
                time.sleep(2)
            else:
                return None
    return None


def find_performance_by_date(df, target_date=None):
    """
    在数据帧中查找指定日期的绩效数据。

    Args:
        df (pd.DataFrame): 包含绩效数据的DataFrame。
        target_date (str, optional): 目标日期，格式为 'YYYY-MM-DD'。如果为None，则查找昨天的数据。

    Returns:
        tuple: (pd.Series or None, str): 返回数据行和对应的日期字符串
    """
    if target_date is None:
        # 获取昨天的日期，并格式化为 YYYY-MM-DD
        yesterday = datetime.now() - timedelta(1)
        target_date = yesterday.strftime('%Y-%m-%d')

    print(f"ℹ️ 正在查找日期为 {target_date} 的数据...")

    # 在'日期'列中查找与目标日期匹配的数据
    # df['日期']已经是datetime对象，所以可以直接比较
    target_data = df[df['日期'].dt.strftime('%Y-%m-%d') == target_date]

    if not target_data.empty:
        # 返回找到的第一行数据和日期
        return target_data.iloc[0], target_date
    else:
        print(f"⚠️ 未找到日期为 {target_date} 的绩效数据，将写入'暂无数据'。")
        # 返回None和日期，用于写入暂无数据
        return None, target_date

def find_yesterday_performance(df):
    """
    在数据帧中查找昨天的绩效数据。（保持向后兼容）

    Args:
        df (pd.DataFrame): 包含绩效数据的DataFrame。

    Returns:
        tuple: (pd.Series or None, str): 返回数据行和对应的日期字符串
    """
    return find_performance_by_date(df)

def main(target_date=None):
    """
    主执行函数

    Args:
        target_date (str, optional): 目标日期，格式为 'YYYY-MM-DD'。如果为None，则处理昨天的数据。
    """
    if target_date:
        print(f"🚀 开始执行义乌仓绩效数据拉取任务（目标日期：{target_date}）...")
    else:
        print("🚀 开始执行义乌仓绩效数据拉取任务（昨天数据）...")

    # 1. 加载配置
    config = load_config()
    if not config:
        return

    # 2. 初始化钉钉表格工具
    try:
        dingtalk_util = DingTalkSheetUtils(config)
    except Exception as e:
        print(f"❌ 初始化钉钉工具失败: {e}")
        return

    # 从配置中获取义乌仓的相关信息
    yiwu_warehouse_config = config.get('yiwu_warehouse_checker', {})
    file_path = yiwu_warehouse_config.get('file_path')
    sheet_name = yiwu_warehouse_config.get('sheet_name')

    # 直接从钉钉配置中获取目标工作表名称
    target_sheet_name = dingtalk_util.sheet_name

    if not all([file_path, sheet_name, target_sheet_name]):
        print("❌ 配置文件中缺少 yiwu_warehouse_checker 的必要信息（file_path, sheet_name）或钉钉表格配置不完整。")
        return

    # 3. 读取本地Excel数据（支持重试）
    performance_df = get_performance_data(file_path, sheet_name, max_retries=3)
    if performance_df is None:
        return

    # 4. 查找指定日期的绩效数据
    if target_date:
        performance_data, actual_date = find_performance_by_date(performance_df, target_date)
    else:
        performance_data, actual_date = find_yesterday_performance(performance_df)

    if performance_data is not None:
        print("✅ 成功获取到绩效数据:")
        print(performance_data)
    else:
        print("ℹ️ 未找到指定日期的数据，将写入'暂无数据'到钉钉表格。")

    # 5. 准备要写入钉钉表格的数据
    # 义乌仓需要读取的字段：'打单', '发货', '入库', '待补货', '丝印开单/调拨'
    try:
        if performance_data is not None:
            # 有数据的情况：将日期格式化回 "YYYY/M/D"，使用 '#' 去掉前导零以兼容Windows
            date_str = performance_data['日期'].strftime('%Y/%#m/%#d')

            # 使用直接索引访问数据
            dadan = performance_data['打单']
            fahuo = performance_data['发货']
            ruku = performance_data['入库']
            daibuhuo = performance_data['待补货']
            siyin_kaidian_diaobo = performance_data['丝印开单/调拨']

            # 准备写入钉钉表格的数据行, 将pandas的NaN值转换为空字符串
            row_to_write = [[
                date_str,
                "" if pd.isna(dadan) else str(dadan),
                "" if pd.isna(fahuo) else str(fahuo),
                "" if pd.isna(ruku) else str(ruku),
                "" if pd.isna(daibuhuo) else str(daibuhuo),
                "" if pd.isna(siyin_kaidian_diaobo) else str(siyin_kaidian_diaobo)
            ]]
        else:
            # 无数据的情况：使用actual_date格式化日期，写入"暂无数据"
            target_datetime = datetime.strptime(actual_date, '%Y-%m-%d')
            date_str = target_datetime.strftime('%Y/%#m/%#d')

            # 准备写入钉钉表格的数据行，所有数据字段都写入"暂无数据"
            row_to_write = [[
                date_str,
                "暂无数据",
                "暂无数据",
                "暂无数据",
                "暂无数据",
                "暂无数据"
            ]]

        print(f"📋 准备写入钉钉的数据: {row_to_write}")

    except KeyError as e:
        print(f"❌ Excel文件中缺少必要的列: {e}。请检查Excel文件标题是否正确。")
        return
    except Exception as e:
        print(f"❌ 处理数据时发生未知错误: {e}")
        return

    # 6. 写入数据到钉钉表格（支持重试）
    success = handle_monthly_summary_with_retry(dingtalk_util, target_sheet_name, row_to_write, date_str, max_retries=3)

    if success:
        print("🏁 任务执行成功完成。")
    else:
        print("❌ 任务执行失败。")

def handle_monthly_summary_with_retry(dingtalk_util, sheet_name, data_row, date_str, max_retries=3):
    """
    处理按月汇总的数据写入逻辑，支持重试和智能数据查找。

    Args:
        dingtalk_util (DingTalkSheetUtils): 钉钉表格工具实例。
        sheet_name (str): 工作表名称。
        data_row (list): 要写入的单行数据。
        date_str (str): 日期字符串，用于查找现有数据行。
        max_retries (int): 最大重试次数。

    Returns:
        bool: 是否成功写入数据。
    """
    for attempt in range(max_retries):
        try:
            print(f"📝 尝试写入钉钉表格 (第{attempt + 1}次/共{max_retries}次)...")

            target_sheet_id = dingtalk_util._get_sheet_id_by_name(sheet_name)
            if not target_sheet_id:
                print(f"❌ 在钉钉表格中找不到名为 '{sheet_name}' 的工作表。")
                return False

            # 根据实际数据日期生成月份标题，而不是当前系统时间
            # date_str格式为 "2025/7/31"，需要解析出年月
            try:
                data_date = datetime.strptime(date_str, '%Y/%m/%d')
                current_month_title = data_date.strftime('%Y年%m月数据汇总')
            except ValueError:
                # 如果解析失败，回退到使用当前时间（向后兼容）
                print(f"⚠️ 无法解析日期格式 {date_str}，使用当前时间生成月份标题")
                current_month_title = datetime.now().strftime('%Y年%m月数据汇总')

            # 查找当前月份标题的位置
            title_location = find_title_row(dingtalk_util, target_sheet_id, current_month_title)

            if title_location:
                # 如果找到了当月标题，先查找是否已有该日期的数据行
                print(f"ℹ️ 找到当月标题 '{current_month_title}'，检查是否已有{date_str}的数据...")
                existing_row = find_existing_date_row(dingtalk_util, target_sheet_id, title_location, date_str)

                if existing_row:
                    # 更新现有行
                    print(f"📝 找到现有日期行（第{existing_row}行），将更新数据...")
                    update_existing_row(dingtalk_util, target_sheet_id, existing_row, data_row)
                else:
                    # 在该标题下追加新数据
                    print(f"📝 未找到现有日期行，将在当月标题下追加新数据...")
                    append_data_under_title(dingtalk_util, target_sheet_id, title_location, data_row)
            else:
                # 如果没找到，则在表格末尾创建新的月度汇总区域
                print(f"ℹ️ 未找到当月标题，将在表格末尾创建新的月度汇总。")
                create_new_month_section(dingtalk_util, target_sheet_id, current_month_title, data_row)

            print("✅ 成功写入钉钉表格数据")
            return True

        except Exception as e:
            print(f"❌ 写入钉钉表格失败: {e}")
            if attempt < max_retries - 1:
                wait_time = 2 ** attempt  # 指数退避：2, 4, 8秒
                print(f"⏳ 等待{wait_time}秒后重试...")
                time.sleep(wait_time)
            else:
                print("❌ 达到最大重试次数，写入失败")
                return False

    return False

def handle_monthly_summary(dingtalk_util, sheet_name, data_row):
    """
    处理按月汇总的数据写入逻辑。（保持向后兼容）

    Args:
        dingtalk_util (DingTalkSheetUtils): 钉钉表格工具实例。
        sheet_name (str): 工作表名称。
        data_row (list): 要写入的单行数据。
    """
    target_sheet_id = dingtalk_util._get_sheet_id_by_name(sheet_name)
    if not target_sheet_id:
        print(f"❌ 在钉钉表格中找不到名为 '{sheet_name}' 的工作表。")
        return

    # 根据数据行中的日期生成月份标题
    # data_row[0] 是日期字符串，格式为 "2025/7/31"
    try:
        date_str = data_row[0][0] if isinstance(data_row[0], list) else data_row[0]
        data_date = datetime.strptime(date_str, '%Y/%m/%d')
        current_month_title = data_date.strftime('%Y年%m月数据汇总')
    except (ValueError, IndexError, TypeError):
        # 如果解析失败，回退到使用当前时间（向后兼容）
        print(f"⚠️ 无法从数据行解析日期，使用当前时间生成月份标题")
        current_month_title = datetime.now().strftime('%Y年%m月数据汇总')

    # 查找当前月份标题的位置
    title_location = find_title_row(dingtalk_util, target_sheet_id, current_month_title)

    if title_location:
        # 如果找到了当月标题，就在该标题下追加数据
        print(f"ℹ️ 找到当月标题 '{current_month_title}'，将在其下方追加数据。")
        append_data_under_title(dingtalk_util, target_sheet_id, title_location, data_row)
    else:
        # 如果没找到，则在表格末尾创建新的月度汇总区域
        print(f"ℹ️ 未找到当月标题，将在表格末尾创建新的月度汇总。")
        create_new_month_section(dingtalk_util, target_sheet_id, current_month_title, data_row)

def find_existing_date_row(dingtalk_util, sheet_id, title_row, date_str):
    """
    在指定月份标题下查找是否已存在指定日期的数据行。

    Args:
        dingtalk_util: 钉钉工具实例
        sheet_id: 工作表ID
        title_row: 月份标题所在行号
        date_str: 要查找的日期字符串（如"2025/1/22"）

    Returns:
        int or None: 如果找到返回行号，否则返回None
    """
    try:
        # 从标题行下一行开始查找（跳过表头）
        start_search_row = title_row + 2
        # 检查接下来的50行（一个月最多31天，留出余量）
        range_to_check = f"A{start_search_row}:A{start_search_row + 50}"

        content = dingtalk_util.get_cell_range(sheet_id, range_to_check)
        if content:
            for i, row in enumerate(content):
                if row and row[0] == date_str:
                    found_row = start_search_row + i
                    print(f"✅ 找到现有日期行在第 {found_row} 行。")
                    return found_row
                # 如果遇到空行或下一个月份标题，停止查找
                if not row or not row[0] or "数据汇总" in str(row[0]):
                    break

        print(f"ℹ️ 未找到日期 {date_str} 的现有数据行。")
        return None

    except Exception as e:
        print(f"❌ 查找现有日期行时出错: {e}")
        return None

def update_existing_row(dingtalk_util, sheet_id, row_number, data_row):
    """
    更新现有数据行。

    Args:
        dingtalk_util: 钉钉工具实例
        sheet_id: 工作表ID
        row_number: 要更新的行号
        data_row: 新的数据行
    """
    try:
        # 更新整行数据（A到F列）
        update_range = f"A{row_number}:F{row_number}"
        success = dingtalk_util.write_cell_range(sheet_id, update_range, data_row)

        if success:
            print(f"✅ 成功更新第 {row_number} 行的数据。")
        else:
            print(f"❌ 更新第 {row_number} 行的数据失败。")

    except Exception as e:
        print(f"❌ 更新现有行时出错: {e}")

def find_title_row(dingtalk_util, sheet_id, title, max_rows=500):
    """在工作表中查找标题所在的行号。"""
    print(f"🔍 正在查找标题: '{title}'")
    # 为了简单起见，我们只检查A列
    range_to_check = f"A1:A{max_rows}"
    try:
        content = dingtalk_util.get_cell_range(sheet_id, range_to_check)
        if content:
            for i, row in enumerate(content):
                if row and row[0] == title:
                    found_row = i + 1
                    print(f"✅ 找到标题在第 {found_row} 行。")
                    return found_row
    except Exception as e:
        print(f"❌ 查找标题时出错: {e}")
    return None

def append_data_under_title(dingtalk_util, sheet_id, title_row, data_row):
    """在指定标题下方追加数据。"""
    # 从标题行下一行开始查找，直到找到一个空行
    start_search_row = title_row + 2  # +1是表头, +2是数据开始行
    last_data_row = start_search_row -1
    
    # 我们检查A列来确定最后的数据行
    range_to_check = f"A{start_search_row}:A{start_search_row + 200}" # 假设一个月不超过200条记录
    try:
        content = dingtalk_util.get_cell_range(sheet_id, range_to_check)
        if content:
            for i, row_content in enumerate(content):
                if not row_content or not row_content[0]:
                    break
                last_data_row = start_search_row + i
    
        next_row = last_data_row + 1
        print(f"📈 将在第 {next_row} 行追加新数据。")
        write_range = f"A{next_row}:F{next_row}"
        dingtalk_util.write_cell_range(sheet_id, write_range, data_row)
    except Exception as e:
        print(f"❌ 追加数据时出错: {e}")


def create_new_month_section(dingtalk_util, sheet_id, title, data_row):
    """在表格末尾创建新的月度汇总区域。"""
    last_row = dingtalk_util.find_last_data_row(sheet_id)
    # 如果工作表为空，则从第1行开始；否则，在最后一行数据下方空一行再开始。
    start_new_section_row = 1 if last_row == 0 else last_row + 2
    
    print(f"📄 将在第 {start_new_section_row} 行创建新的月度汇总区域。")
    
    # 写入月份标题
    title_range = f"A{start_new_section_row}"
    dingtalk_util.write_cell_range(sheet_id, title_range, [[title]])
    dingtalk_util.merge_cells(sheet_id, f"A{start_new_section_row}:F{start_new_section_row}")
    dingtalk_util.set_cell_alignment(sheet_id, f"A{start_new_section_row}", alignment='center')

    # 写入表头
    header_row = start_new_section_row + 1
    headers = [['日期', '打单', '发货', '入库', '待补货', '丝印开单/调拨']]
    header_range = f"A{header_row}:F{header_row}"
    dingtalk_util.write_cell_range(sheet_id, header_range, headers)

    # 写入第一行数据
    data_start_row = header_row + 1
    data_range = f"A{data_start_row}:F{data_start_row}"
    dingtalk_util.write_cell_range(sheet_id, data_range, data_row)


if __name__ == '__main__':
    # 切换工作目录到脚本所在目录，以确保相对路径正确
    os.chdir(os.path.dirname(os.path.abspath(__file__)))

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='义乌仓绩效数据拉取脚本')
    parser.add_argument('-d', type=str, help='指定要处理的日期，格式为YYYY-MM-DD（如：2025-01-22）。如果不指定，则处理昨天的数据。')

    args = parser.parse_args()

    # 验证日期格式
    target_date = None
    if args.d:
        try:
            # 验证日期格式是否正确
            datetime.strptime(args.d, '%Y-%m-%d')
            target_date = args.d
            print(f"ℹ️ 使用指定日期: {target_date}")
        except ValueError:
            print("❌ 日期格式错误，请使用YYYY-MM-DD格式（如：2025-01-22）")
            exit(1)
    else:
        print("ℹ️ 未指定日期，将处理昨天的数据")

    # 执行主函数
    main(target_date)