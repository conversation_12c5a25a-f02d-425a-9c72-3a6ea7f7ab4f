#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
义乌仓绩效数据拉取项目打包脚本
使用PyInstaller将Python项目打包成exe可执行文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print("✅ PyInstaller已安装")
        return True
    except ImportError:
        print("❌ PyInstaller未安装")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("📦 正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ PyInstaller安装失败: {e}")
        return False

def create_spec_file():
    """创建PyInstaller的spec配置文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['义乌仓绩效数据同步.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config.json', '.'),
        ('dingtalk_sheet_utils.py', '.'),
    ],
    hiddenimports=[
        'pandas',
        'openpyxl',
        'requests',
        'urllib3',
        'argparse',
        'json',
        'datetime',
        'time'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='义乌仓绩效数据拉取',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open('义乌仓绩效数据拉取.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 已创建spec配置文件")

def build_exe():
    """执行打包"""
    print("🔨 开始打包exe文件...")
    
    try:
        # 使用spec文件进行打包
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "义乌仓绩效数据拉取.spec"]
        
        print(f"📋 执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ 打包成功！")
            print(f"📁 exe文件位置: {os.path.abspath('dist/义乌仓绩效数据拉取.exe')}")
            return True
        else:
            print("❌ 打包失败！")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 打包过程中发生异常: {e}")
        return False



def copy_config_files():
    """复制配置文件到dist目录"""
    try:
        if os.path.exists('config.json'):
            shutil.copy2('config.json', 'dist/')
            print("✅ 已复制config.json到dist目录")

    except Exception as e:
        print(f"⚠️ 复制配置文件时出错: {e}")

def clean_build_files():
    """清理构建过程中的临时文件"""
    try:
        # 删除build目录
        if os.path.exists('build'):
            shutil.rmtree('build')
            print("🧹 已清理build目录")
        
        # 删除spec文件
        if os.path.exists('义乌仓绩效数据拉取.spec'):
            os.remove('义乌仓绩效数据拉取.spec')
            print("🧹 已清理spec文件")
            
    except Exception as e:
        print(f"⚠️ 清理临时文件时出错: {e}")

def main():
    """主函数"""
    print("🚀 义乌仓部绩效数据拉取项目打包工具")
    print("=" * 50)
    
    # 检查当前目录是否包含必要文件
    required_files = ['义乌仓绩效数据同步.py', 'dingtalk_sheet_utils.py', 'config.json']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        print("请确保在项目根目录下运行此脚本")
        return
    
    # 检查并安装PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            print("❌ 无法安装PyInstaller，打包失败")
            return
    
    # 创建spec文件
    create_spec_file()
    
    # 执行打包
    if build_exe():
        # 复制配置文件
        copy_config_files()

        # 清理临时文件
        clean_build_files()

        print("\n" + "=" * 50)
        print("🎉 打包完成！")
        print(f"📁 输出目录: {os.path.abspath('dist')}")
        print("\n文件列表:")
        print("- 义乌仓绩效数据拉取.exe (主程序)")
        print("- config.json (配置文件)")

    else:
        print("❌ 打包失败，请检查错误信息")

if __name__ == '__main__':
    main()
